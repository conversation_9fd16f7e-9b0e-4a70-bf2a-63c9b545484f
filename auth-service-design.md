# Internal Tudip Authentication Service

## Architecture Overview

This design transforms the existing Kuber application's authentication system into an internal authentication service for Tudip applications. Built with Python FastAPI, the service provides Google OAuth authentication and user validation against the existing Kuber users database for internal Tudip applications only. The service focuses on simplicity and internal integration without external API complexity.

## Python FastAPI Backend Structure

### 1. Internal Authentication Service Components
```
kuber-auth-service/
├── app/
│   ├── main.py                         # FastAPI application entry point
│   ├── config.py                       # Configuration settings
│   ├── database.py                     # Database connection and models
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── router.py                   # Authentication endpoints
│   │   ├── service.py                  # Authentication business logic
│   │   ├── models.py                   # Pydantic models for auth
│   │   ├── google_oauth.py             # Google OAuth integration
│   │   └── jwt_handler.py              # JWT token management
│   ├── users/
│   │   ├── __init__.py
│   │   ├── router.py                   # User endpoints
│   │   ├── service.py                  # User business logic
│   │   └── models.py                   # User Pydantic models
│   ├── middleware/
│   │   ├── __init__.py
│   │   ├── auth_middleware.py          # JWT validation middleware
│   │   └── cors_middleware.py          # CORS configuration
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── database_utils.py           # Database helper functions
│   │   └── security_utils.py           # Security utilities
│   └── schemas/
│       ├── __init__.py
│       ├── auth_schemas.py             # Authentication request/response schemas
│       └── user_schemas.py             # User data schemas
├── requirements.txt                    # Python dependencies
├── Dockerfile                          # Docker configuration
├── docker-compose.yml                 # Docker compose for development
└── README.md                          # Setup and usage documentation
```

### 2. Database Schema (No Changes Required)

The authentication service will use the existing Kuber database without any schema modifications:

```sql
-- Existing Kuber database tables used as-is:

-- users table: Contains all Tudip employees
-- Fields used by auth service:
-- - id: Primary key
-- - username: Email address (used for authentication)
-- - first_name: User's first name
-- - last_name: User's last name
-- - employee_id: Unique employee identifier
-- - is_active: Account status (0 = inactive, 1 = active)
-- - id_tenant: Tenant/organization identifier

-- No additional tables needed for internal authentication service
-- Service connects directly to existing Kuber MySQL database
```

### 3. Internal API Endpoints

#### Authentication Endpoints
These endpoints provide authentication services for internal Tudip applications:

**Authentication API (`/auth`)**
- `POST /auth/login` - Google OAuth login for internal applications
  - Input: Google OAuth token
  - Output: JWT token + basic user info OR "User not present in the organization" error
  - Example Response (Success):
    ```json
    {
      "success": true,
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "user": {
        "id": 123,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "employee_id": "TUD001"
      }
    }
    ```
  - Example Response (User Not Found):
    ```json
    {
      "success": false,
      "message": "User not present in the organization"
    }
    ```

- `POST /auth/validate` - Validate JWT token
  - Input: JWT token
  - Output: User info if valid, error if invalid

- `GET /auth/me` - Get current user information
  - Input: JWT token in Authorization header
  - Output: Basic user info (name, email, employee ID)

#### Health Check
- `GET /health` - Service health check
- `GET /docs` - FastAPI automatic documentation (Swagger UI)

### 4. Python FastAPI Configuration

#### Environment Variables
Configuration for the Python FastAPI authentication service:

```env
# Database Configuration
DATABASE_URL="mysql+pymysql://username:password@kuber-db-host:3306/kuber_database"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_WORKSPACE_DOMAIN="tudip.com"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_ALGORITHM="HS256"
JWT_EXPIRES_IN_HOURS=24  # Token expiration time

# Service Configuration
SERVICE_HOST="0.0.0.0"
SERVICE_PORT=8000
DEBUG=false

# CORS Configuration (for internal Tudip applications)
CORS_ORIGINS="http://localhost:3000,https://app1.tudip.internal,https://app2.tudip.internal"
CORS_ALLOW_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FORMAT="json"

# Documentation
DOCS_ENABLED=true
DOCS_URL="/docs"
REDOC_URL="/redoc"
```

### 5. Internal Application Integration Patterns

#### A. Direct HTTP API Integration
```python
# Python application integration
import requests

# Login with Google OAuth token
auth_response = requests.post('http://auth-service:8000/auth/login',
    json={'google_token': 'oauth-token'},
    headers={'Content-Type': 'application/json'}
)

result = auth_response.json()
if result['success']:
    # User exists in Kuber database
    token = result['token']
    user = result['user']
    # user contains: { id, email, first_name, last_name, employee_id }
else:
    # User not found in organization
    print(result['message'])  # "User not present in the organization"

# Validate token
validation_response = requests.post('http://auth-service:8000/auth/validate',
    json={'token': 'jwt-token'},
    headers={'Content-Type': 'application/json'}
)
```

#### B. JavaScript/React Integration
```javascript
// Frontend JavaScript integration
class TudipAuth {
    constructor(authServiceUrl) {
        this.authServiceUrl = authServiceUrl;
        this.token = localStorage.getItem('tudip_token');
    }

    async loginWithGoogle(googleToken) {
        try {
            const response = await fetch(`${this.authServiceUrl}/auth/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ google_token: googleToken })
            });

            const result = await response.json();
            if (result.success) {
                this.token = result.token;
                localStorage.setItem('tudip_token', this.token);
                return result.user;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    }

    async validateToken() {
        if (!this.token) return null;

        try {
            const response = await fetch(`${this.authServiceUrl}/auth/validate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: this.token })
            });

            const result = await response.json();
            return result.success ? result.user : null;
        } catch (error) {
            console.error('Token validation failed:', error);
            return null;
        }
    }

    logout() {
        this.token = null;
        localStorage.removeItem('tudip_token');
    }
}

// Usage
const auth = new TudipAuth('http://auth-service:8000');
```

#### C. Node.js/Express Integration
```javascript
// Node.js Express middleware
const axios = require('axios');

const authMiddleware = (authServiceUrl) => {
    return async (req, res, next) => {
        const token = req.headers.authorization?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }

        try {
            const response = await axios.post(`${authServiceUrl}/auth/validate`, {
                token: token
            });

            if (response.data.success) {
                req.user = response.data.user;
                next();
            } else {
                res.status(401).json({ error: 'Invalid token' });
            }
        } catch (error) {
            res.status(401).json({ error: 'Authentication failed' });
        }
    };
};

// Usage
app.use('/api/protected', authMiddleware('http://auth-service:8000'));
```

#### D. PHP Integration
```php
// PHP integration example
class TudipAuthClient {
    private $authServiceUrl;

    public function __construct($authServiceUrl) {
        $this->authServiceUrl = $authServiceUrl;
    }

    public function validateToken($token) {
        $response = file_get_contents($this->authServiceUrl . '/auth/validate', false,
            stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => 'Content-Type: application/json',
                    'content' => json_encode(['token' => $token])
                ]
            ])
        );

        $result = json_decode($response, true);
        return $result['success'] ? $result['user'] : null;
    }
}

// Usage
$auth = new TudipAuthClient('http://auth-service:8000');
$user = $auth->validateToken($token);
```

### 6. Security Features

#### Email Domain Validation
- Enforce @tudip.com email domain through Google Workspace
- Google OAuth token validation
- User validation against existing Kuber users database

#### Token Security
- JWT with configurable expiration (24 hours default)
- HS256 algorithm for token signing
- Secure token storage and validation
- CORS control for internal Tudip applications

#### User Validation Logic
- **Google OAuth Validation**: Verify Google token is valid and from @tudip.com domain
- **Organization Membership Check**: Check if user exists in Kuber users table
- **Active User Validation**: Ensure user account is active (is_active = 1)
- **Response Logic**:
  - If user exists and is active: Return JWT token with user info
  - If user doesn't exist: Return "User not present in the organization" error
  - If user exists but inactive: Return "User account is deactivated" error

#### Internal Network Security
- **Network Isolation**: Service runs on internal network only
- **CORS Control**: Configured for internal Tudip application domains
- **Request Logging**: Track authentication attempts and user access
- **Token Validation**: All tokens validated against Kuber's user database
- **Minimal Attack Surface**: Simple API with limited endpoints

### 7. Python FastAPI Deployment

#### Docker Deployment
```yaml
# docker-compose.yml for Python FastAPI auth service
version: '3.8'
services:
  tudip-auth-service:
    build: ./kuber-auth-service
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://root:${DB_PASSWORD}@mysql:3306/kuber
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGINS=http://localhost:3000,https://app1.tudip.internal
    depends_on:
      - mysql
    networks:
      - tudip-internal

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: kuber
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - tudip-internal

volumes:
  mysql_data:

networks:
  tudip-internal:
    driver: bridge
```

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY app/ ./app/

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Production Considerations
- **Load Balancing**: External API endpoints can be load balanced separately
- **CDN Integration**: SDK files can be served via CDN
- **SSL/TLS**: HTTPS required for external API access
- **Monitoring**: Enhanced logging for external API usage
- **Backup**: Regular database backups including new external API tables

### 8. Python Dependencies and Requirements

#### Requirements.txt
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
sqlalchemy==2.0.23
pymysql==1.1.0
cryptography==41.0.8
google-auth==2.25.2
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0
passlib[bcrypt]==1.7.4
```

#### Key Python Libraries
- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: SQL toolkit and ORM for database operations
- **PyMySQL**: MySQL database connector
- **python-jose**: JWT token handling
- **google-auth**: Google OAuth integration
- **pydantic**: Data validation and settings management
- **uvicorn**: ASGI server for running FastAPI applications

### 9. Implementation Strategy

#### Phase 1: Python FastAPI Service Development (2-3 weeks)
1. **Setup FastAPI Project**: Create Python FastAPI project structure
2. **Database Integration**: Connect to existing Kuber MySQL database
3. **Google OAuth Integration**: Implement Google OAuth token validation
4. **JWT Implementation**: Add JWT token generation and validation
5. **Basic Testing**: Unit tests for authentication flow

#### Phase 2: Internal API Development (1-2 weeks)
1. **Authentication Endpoints**: Implement login, validate, and user info endpoints
2. **Error Handling**: Implement "User not present in organization" logic
3. **Security Features**: Add CORS, logging, and basic security measures
4. **Documentation**: Generate FastAPI automatic documentation
5. **Integration Testing**: Test with existing Kuber database

#### Phase 3: Pilot Integration (1-2 weeks)
1. **Select Pilot Application**: Choose one internal Tudip application
2. **Integration Testing**: Test authentication flow with pilot application
3. **User Validation Testing**: Test user existence validation
4. **Error Handling Testing**: Test error scenarios and responses
5. **Performance Testing**: Ensure acceptable response times

#### Phase 4: Production Deployment (1-2 weeks)
1. **Production Setup**: Deploy Python FastAPI service to production
2. **Network Configuration**: Configure internal network access
3. **Monitoring Setup**: Implement logging and health checks
4. **Security Review**: Final security review and hardening
5. **Documentation**: Complete deployment and integration documentation

#### Phase 5: Rollout to Internal Applications (2-4 weeks)
1. **Team Training**: Train internal development teams
2. **Gradual Migration**: Migrate internal applications one by one
3. **Integration Support**: Provide support for integration issues
4. **Monitoring**: Monitor authentication success rates and performance
5. **Feedback Collection**: Gather feedback for improvements

#### Phase 6: Maintenance (Ongoing)
1. **Bug Fixes**: Address any authentication issues
2. **Security Updates**: Keep dependencies and OAuth integration updated
3. **Performance Monitoring**: Monitor service performance and database load
4. **User Database Maintenance**: Ensure user data stays synchronized

### 10. Benefits

#### For Internal Applications
- **Consistent Authentication**: Same Google OAuth flow across all internal Tudip applications
- **Reduced Development Time**: Simple HTTP API eliminates authentication development
- **Easy Integration**: Basic REST API with clear error messages
- **Organization Validation**: Automatic check against Tudip employee database
- **No User Management**: Applications don't need to manage user data
- **Language Agnostic**: Works with any programming language that can make HTTP requests

#### For Users
- **Familiar Login**: Same Google OAuth login across all internal applications
- **Clear Feedback**: Clear error message if not authorized for application
- **Single Identity**: One Tudip identity across all internal applications
- **No Multiple Accounts**: No need to create separate accounts per application

#### For IT/Security
- **Centralized User Control**: Manage user access from existing Kuber user database
- **Simple Audit**: Track authentication attempts across internal applications
- **Easy User Management**: Add/remove users in Kuber affects all applications
- **Consistent Security**: Same security standards across all internal applications
- **Quick Response**: Deactivate users instantly across all applications
- **Internal Network**: Service runs on internal network only

#### For Development Teams
- **Simple Implementation**: Basic HTTP API with minimal complexity
- **Clear Documentation**: FastAPI automatic documentation
- **Minimal Maintenance**: No complex role or permission management
- **Fast Integration**: Quick setup with simple HTTP calls
- **Reliable Service**: Python FastAPI with proven database integration
- **Technology Flexibility**: Use any programming language or framework

### 11. Implementation Considerations

#### Backward Compatibility
- Existing Kuber application functionality remains completely unchanged
- No changes to existing Kuber database schema
- New service is completely separate from existing Kuber backend

#### Performance Impact
- Lightweight Python FastAPI service
- Simple database queries against existing users table
- Minimal overhead on existing Kuber database
- Fast JWT token validation

#### Maintenance
- Simple Python codebase with minimal complexity
- Clear error handling and logging
- Easy to troubleshoot authentication issues
- Standard Python deployment and monitoring

#### Database Integration
- Direct connection to existing Kuber MySQL database
- Read-only access to users table
- No data synchronization needed
- Leverages existing user management processes
