from google.oauth2 import id_token
from google.auth.transport import requests
from config import Config

class GoogleOAuthError(Exception):
    pass

def verify_google_token(token: str) -> dict:
    """
    Verifies the Google OAuth token and returns user info if valid and from the correct domain.
    Raises GoogleOAuthError if invalid or not @tudip.com.
    """
    try:
        idinfo = id_token.verify_oauth2_token(token, requests.Request(), Config.GOOGLE_CLIENT_ID)
        email = idinfo.get("email")
        # If you want to restrict to @tudip.com, uncomment the next two lines:
        # if not email or not email.endswith("@tudip.com"):
        #     raise GoogleOAuthError("Email is not a valid Tudip email.")
        return {
            "email": email,
            "first_name": idinfo.get("given_name", ""),
            "last_name": idinfo.get("family_name", ""),
            "picture": idinfo.get("picture", ""),
            "sub": idinfo.get("sub", ""),
        }
    except Exception as e:
        raise GoogleOAuthError(f"Invalid Google OAuth token: {str(e)}")
