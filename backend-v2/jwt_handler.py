from jose import jwt, J<PERSON><PERSON>rror, ExpiredSignatureError
from datetime import datetime, timedelta
from config import Config

class JWTAuthError(Exception):
    pass

def create_jwt_token(user: dict) -> str:
    """
    Create a JWT token for the user.
    user: dict with at least 'id', 'email', 'first_name', 'last_name', 'employee_id'
    """
    payload = {
        "sub": str(user["id"]),
        "email": user["email"],
        "first_name": user.get("first_name", ""),
        "last_name": user.get("last_name", ""),
        "employee_id": user.get("employee_id", ""),
        "exp": datetime.utcnow() + timedelta(hours=Config.JWT_EXPIRES_IN_HOURS),
        "iat": datetime.utcnow(),
    }
    token = jwt.encode(payload, Config.JWT_SECRET, algorithm=Config.JWT_ALGORITHM)
    return token

def decode_jwt_token(token: str) -> dict:
    """
    Decode and validate a JWT token. Returns the payload if valid.
    Raises JWTAuthError if invalid or expired.
    """
    try:
        payload = jwt.decode(token, Config.JWT_SECRET, algorithms=[Config.JWT_ALGORITHM])
        return payload
    except ExpiredSignatureError:
        raise JWTAuthError("Token has expired.")
    except JWTError as e:
        raise JWTAuthError(f"Invalid token: {str(e)}")
