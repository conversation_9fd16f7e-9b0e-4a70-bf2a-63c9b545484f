from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form, Body, Request, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from typing import List, Optional
import os
import tempfile
import io

from document_processor import DocumentProcessor
from test_case_agent import TestCaseGeneratorSimple
from vector_store import VectorStoreManager
from langchain.schema import Document
from fpdf import FPDF

# --- Auth imports ---
from db import get_user_by_email
from google_oauth import verify_google_token, GoogleOAuthError
from jwt_handler import create_jwt_token, decode_jwt_token, JWTAuthError
from config import Config

from starlette.requests import Request
from starlette.middleware.base import BaseHTTPMiddleware

app = FastAPI(
    title="Testify API",
    description="API for AI-powered test case generation from requirements documents.",
    version="1.0.0"
)

# CORS setup
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to your frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add this middleware class
class LargeUploadMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Increase the max size for the specific upload endpoint
        if request.url.path == "/upload":
            # Set to a large value (e.g., 50MB)
            request._body_size_limit = 50 * 1024 * 1024
        return await call_next(request)

# Add the middleware to the app
app.add_middleware(LargeUploadMiddleware)

# --- Authentication Endpoints ---

from pydantic import BaseModel

class GoogleLoginRequest(BaseModel):
    google_token: str

@app.post("/auth/login")
async def auth_login(data: GoogleLoginRequest):
    """
    Login with Google OAuth ID token (credential). Returns JWT and user info if valid Tudip user.
    """
    try:
        # 1. Verify Google token and Tudip domain
        google_info = verify_google_token(data.google_token)
        email = google_info["email"]

        # 2. Check user in DB
        user = get_user_by_email(email)
        if not user:
            return JSONResponse(status_code=401, content={"success": False, "message": "User not present in the organization"})
        if not user.is_active:
            return JSONResponse(status_code=403, content={"success": False, "message": "User account is deactivated"})

        # 3. Issue JWT
        user_info = {
            "id": user.id,
            "email": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "employee_id": user.employee_id,
        }
        token = create_jwt_token(user_info)
        return {
            "success": True,
            "token": token,
            "user": user_info
        }
    except GoogleOAuthError as e:
        return JSONResponse(status_code=401, content={"success": False, "message": str(e)})
    except Exception as e:
        return JSONResponse(status_code=500, content={"success": False, "message": f"Internal error: {str(e)}"})

class JWTValidateRequest(BaseModel):
    token: str

@app.post("/auth/validate")
async def auth_validate(data: JWTValidateRequest):
    """
    Validate JWT token. Returns user info if valid.
    """
    try:
        payload = decode_jwt_token(data.token)
        user_info = {
            "id": payload["sub"],
            "email": payload["email"],
            "first_name": payload.get("first_name", ""),
            "last_name": payload.get("last_name", ""),
            "employee_id": payload.get("employee_id", ""),
        }
        return {"success": True, "user": user_info}
    except JWTAuthError as e:
        return JSONResponse(status_code=401, content={"success": False, "message": str(e)})
    except Exception as e:
        return JSONResponse(status_code=500, content={"success": False, "message": f"Internal error: {str(e)}"})

@app.get("/auth/me")
async def auth_me(authorization: Optional[str] = Header(None)):
    """
    Get current user info from JWT in Authorization header.
    """
    if not authorization or not authorization.startswith("Bearer "):
        return JSONResponse(status_code=401, content={"success": False, "message": "Missing or invalid Authorization header"})
    token = authorization.replace("Bearer ", "")
    try:
        payload = decode_jwt_token(token)
        user_info = {
            "id": payload["sub"],
            "email": payload["email"],
            "first_name": payload.get("first_name", ""),
            "last_name": payload.get("last_name", ""),
            "employee_id": payload.get("employee_id", ""),
        }
        return {"success": True, "user": user_info}
    except JWTAuthError as e:
        return JSONResponse(status_code=401, content={"success": False, "message": str(e)})
    except Exception as e:
        return JSONResponse(status_code=500, content={"success": False, "message": f"Internal error: {str(e)}"})

# --- End Auth Endpoints ---

@app.post("/upload")
async def upload_document(file: UploadFile = File(...)):
    """
    Accepts a requirements document, processes it, and returns extracted text.
    """
    try:
        doc_processor = DocumentProcessor()
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[-1]) as tmp_file:
            tmp_file.write(await file.read())
            tmp_file_path = tmp_file.name

        documents = doc_processor.process_document(tmp_file_path)
        requirements_text = "\n\n".join([doc.page_content for doc in documents])

        os.unlink(tmp_file_path)
        return {"success": True, "requirements_text": requirements_text}
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

@app.post("/generate")
async def generate_test_cases(
    requirements_text: str = Form(...),
    test_types: List[str] = Form(...),
    detail_level: str = Form(...),
    case_frequency: List[str] = Form(None),
    columns: List[str] = Form(None),
    custom_columns: str = Form(None),
    persona: str = Form(None)
):
    """
    Generates test cases from requirements text, test types, detail level, and user-selected case frequency.
    Also supports custom columns, generating data for them using the LLM.
    """
    try:
        generator = TestCaseGeneratorSimple()

        # Parse custom columns if provided
        parsed_custom_columns = []
        if custom_columns:
            try:
                import json
                parsed_custom_columns = json.loads(custom_columns)
            except json.JSONDecodeError:
                pass

        # Single context with all case frequencies
        context = {
            "test_types": test_types,
            "case_frequencies": case_frequency or ["Smoke"],
            "detail_level": detail_level,
            "source_file": "generate_api",
            "file_type": "text",
            "columns": columns or [],
            "custom_columns": parsed_custom_columns,
            "persona": persona
        }
        
        # Make a single LLM call with all frequencies
        result = generator.generate_test_cases_with_context(requirements_text, context)
        all_test_cases = []
        
        if result and "test_cases" in result:
            all_test_cases = result["test_cases"]
            
            # Ensure case_frequency field exists in each test case
            for tc in all_test_cases:
                # Map from "Case Frequency" to "case_frequency" if needed
                if "Case Frequency" in tc and not "case_frequency" in tc:
                    tc["case_frequency"] = tc["Case Frequency"]
                # Set default if missing
                if not "case_frequency" in tc:
                    tc["case_frequency"] = case_frequency[0] if case_frequency and len(case_frequency) > 0 else "Smoke"

        # Embed the requirements text in the vector store after generating test cases
        try:
            vs_manager = VectorStoreManager()
            if not vs_manager.load_vector_store():
                vs_manager.create_vector_store([])
            doc_obj = Document(page_content=requirements_text, metadata={"source": "generate_api"})
            vs_manager.add_documents([doc_obj])
        except Exception as embed_err:
            import logging
            logging.warning(f"Vector store embedding error: {embed_err}")
            
        return {"success": True, "test_cases": all_test_cases}
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

@app.get("/")
def root():
    return {"message": "Testify FastAPI backend is running."}

@app.post("/export/pdf")
async def export_pdf(test_cases: List[dict] = Body(...)):
    """
    Accepts a list of test cases and returns a generated PDF file.
    """
    try:
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        pdf.cell(0, 10, "Test Cases", ln=True, align="C")
        pdf.ln(5)
        for i, tc in enumerate(test_cases, 1):
            pdf.set_font("Arial", "B", 12)
            title = tc.get("title") or tc.get("Test Case ID") or f"Test Case {i}"
            pdf.cell(0, 10, f"Test Case {i}: {title}", ln=True)
            pdf.set_font("Arial", "", 11)
            pdf.cell(0, 8, f"Type: {tc.get('type', '') or tc.get('Type', '')}", ln=True)
            # Do NOT include Test Depth in PDF export
            if "description" in tc or "Description" in tc:
                desc = tc.get("description") or tc.get("Description")
                pdf.multi_cell(0, 8, f"Description: {desc}")
            if "steps" in tc or "Steps" in tc:
                steps = tc.get("steps") or tc.get("Steps")
                if isinstance(steps, list):
                    steps_str = "\n".join([f"{j+1}. {step}" for j, step in enumerate(steps)])
                else:
                    steps_str = str(steps)
                pdf.multi_cell(0, 8, "Steps:\n" + steps_str)
            if "expected_result" in tc or "Expected Result" in tc:
                exp = tc.get("expected_result") or tc.get("Expected Result")
                pdf.multi_cell(0, 8, f"Expected Result: {exp}")
            pdf.ln(4)
        pdf_bytes = pdf.output(dest='S').encode('latin1')
        return StreamingResponse(io.BytesIO(pdf_bytes), media_type="application/pdf", headers={
            "Content-Disposition": "attachment; filename=test_cases.pdf"
        })
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

import csv

from pydantic import BaseModel

class TestCasesRequest(BaseModel):
    test_cases: list[dict]
    selected_columns: list[str] = None  # Optional field for selected columns

@app.post("/export/csv")
async def export_csv(request: TestCasesRequest):
    """
    Accepts a list of test cases and returns a generated CSV file.
    """
    try:
        output = io.StringIO()
        writer = csv.writer(output)
        # Write header
        writer.writerow(["S.NO", "Title", "Type", "Steps", "Expected Result"])
        # Write test cases
        for i, tc in enumerate(request.test_cases, 1):
            title = tc.get("title", f"Test Case {i}")
            tc_type = tc.get("type", "")
            steps = tc.get("steps", [])
            if isinstance(steps, list):
                steps_str = "\n".join([f"{j+1}. {step}" for j, step in enumerate(steps)])
            else:
                steps_str = str(steps)
            expected_result = tc.get("expected_result", "")
            writer.writerow([i, title, tc_type, steps_str, expected_result])
        output.seek(0)
        return StreamingResponse(io.BytesIO(output.getvalue().encode("utf-8")), media_type="text/csv", headers={
            "Content-Disposition": "attachment; filename=test_cases.csv"
        })
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

import pandas as pd

@app.post("/export/excel")
async def export_excel(request: TestCasesRequest):
    """
    Accepts a list of test cases and returns a generated Excel (.xlsx) file.
    Supports custom columns.
    """
    try:
        # Use selected columns if provided, otherwise fall back to all available columns
        if request.selected_columns:
            # Use only the columns that the user explicitly selected
            ordered_columns = request.selected_columns
        else:
            # Fallback: Determine all columns (default + custom) in order
            default_columns = [
                "Test Case ID",
                "Type",
                "Case Strategy",
                "Test Case Description",
                "Preconditions",
                "Test Steps",
                "Test Data",
                "Expected Result",
                "Actual Result",
                "Status (Pass/Fail)",
                "Comments"
            ]

            # Find all unique columns from test cases (including custom ones)
            all_columns = set(default_columns)
            for tc in request.test_cases:
                all_columns.update(tc.keys())

            # Order columns: default columns first, then custom columns
            ordered_columns = []
            for col in default_columns:
                if col in all_columns:
                    ordered_columns.append(col)

            # Add custom columns (those not in default_columns)
            custom_columns = [col for col in all_columns if col not in default_columns]
            ordered_columns.extend(sorted(custom_columns))  # Sort custom columns alphabetically
        data = []
        for i, tc in enumerate(request.test_cases, 1):
            row = {}

            # Handle default columns with special mapping logic
            for col in ordered_columns:
                if col == "Test Case ID":
                    row[col] = tc.get("Test Case ID") or tc.get("id") or f"TC-{i}"
                elif col == "Type":
                    row[col] = tc.get("Type") or tc.get("type", "")
                elif col == "Case Strategy":
                    row[col] = tc.get("Case Strategy") or tc.get("case_strategy", "") or tc.get("Case frequency") or tc.get("case_frequency", "")
                elif col == "Test Case Description":
                    row[col] = tc.get("Description") or tc.get("description", f"Test Case {i}")
                elif col == "Preconditions":
                    row[col] = tc.get("Preconditions") or tc.get("preconditions", "")
                elif col == "Test Steps":
                    steps = tc.get("Steps") or tc.get("steps", [])
                    if isinstance(steps, list):
                        steps_str = "\n".join([f"{j+1}. {step}" for j, step in enumerate(steps)])
                    else:
                        steps_str = str(steps)
                    row[col] = steps_str
                elif col == "Test Data":
                    test_data = tc.get("Test Data") or tc.get("test_data", "")
                    if isinstance(test_data, dict):
                        import json
                        test_data = json.dumps(test_data)
                    row[col] = test_data
                elif col == "Expected Result":
                    row[col] = tc.get("Expected Result") or tc.get("expected_result", "")
                elif col == "Actual Result":
                    row[col] = tc.get("Actual Result", "")
                elif col == "Status (Pass/Fail)":
                    row[col] = tc.get("Status (Pass/Fail)", "")
                elif col == "Comments":
                    row[col] = tc.get("Comments", "")
                else:
                    # For custom columns, use the value directly from test case
                    value = tc.get(col, "")
                    # Convert complex data types to strings
                    if isinstance(value, (dict, list)):
                        import json
                        value = json.dumps(value)
                    row[col] = str(value)

            data.append(row)

        df = pd.DataFrame(data, columns=ordered_columns)
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, index=False, sheet_name="Test Cases")
            workbook = writer.book
            worksheet = writer.sheets["Test Cases"]

            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

            # Header formatting
            header_font = Font(bold=True)
            header_fill = PatternFill("solid", fgColor="D9E1F2")  # Light blue
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill

            # Freeze header row
            worksheet.freeze_panes = worksheet["A2"]

            # Text wrapping and borders
            thin_border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                 top=Side(style='thin'), bottom=Side(style='thin'))
            for row in worksheet.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical="top")
                    cell.border = thin_border

            # Auto-fit column widths
            for column_cells in worksheet.columns:
                length = max(len(str(cell.value) if cell.value else "") for cell in column_cells)
                worksheet.column_dimensions[column_cells[0].column_letter].width = length + 2

        output.seek(0)
        return StreamingResponse(output, media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", headers={
            "Content-Disposition": "attachment; filename=test_cases.xlsx"
        })
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})
