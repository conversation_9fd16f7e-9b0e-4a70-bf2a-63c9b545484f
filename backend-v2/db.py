from sqlalchemy import create_engine, Column, Integer, String, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import sessionmaker, declarative_base, Session
from config import Config

# SQLAlchemy base
Base = declarative_base()

# User model (matches Kuber users table fields needed for auth)
class User(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True)
    username = Column(String(255), unique=True, nullable=False)  # Email
    first_name = Column(String(255))
    last_name = Column(String(255))
    employee_id = Column(String(255))
    is_active = Column(Boolean)
    id_tenant = Column(Integer)

# Create engine and session factory
engine = create_engine(Config.DATABASE_URL, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_user_by_email(email: str) -> User | None:
    """Fetch user by email (username) from the users table."""
    session: Session = SessionLocal()
    try:
        user = session.query(User).filter(User.username == email).first()
        return user
    finally:
        session.close()
