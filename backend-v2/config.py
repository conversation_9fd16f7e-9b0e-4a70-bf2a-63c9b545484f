import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Google API Key (legacy, not used for OAuth login)
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

    # Vector DB settings
    VECTOR_DB_PATH = "./vector_db"
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200

    # Model settings
    EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
    LLM_MODEL = "gemini-1.5-flash"
    TEMPERATURE = 0.1

    # --- Authentication Service Settings ---
    # MySQL Database
    DATABASE_URL = os.getenv("DATABASE_URL")

    # Google OAuth
    GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
    GOOGLE_SECRET = os.getenv("GOOGLE_SECRET")
    # GOOGLE_WORKSPACE_DOMAIN is not needed unless you want to restrict domain

    # JWT
    JWT_SECRET = os.getenv("JWT_SECRET", "super-secret-key")
    JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_EXPIRES_IN_HOURS = int(os.getenv("JWT_EXPIRES_IN_HOURS", "24"))
