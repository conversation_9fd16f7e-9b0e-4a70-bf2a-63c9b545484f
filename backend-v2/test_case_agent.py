#!/usr/bin/env python3
"""
Simplified Test Case Agent using Gemini AI without vector storage dependencies
"""

from typing import List, Dict, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, AIMessage
from config import Config
import json
import re  # Make sure re is imported at the top level
from datetime import datetime

class TestCaseGeneratorSimple:
    PERSONA_INTROS = {
        "thorough_validator": "You are a senior QA engineer with expertise in functional, boundary, edge-case, and negative testing. Focus on thoroughness, input validation, and business rules.",
        "step_by_step_guide": "Act as an expert QA analyst. Provide clear, step-by-step test cases with objectives and expected results.",
        "automation_friendly": "You are a QA expert designing test cases for both manual and automated testing. Structure cases for easy automation.",
        "real_world_user": "Act as a real-world user. Generate realistic, end-to-end scenarios reflecting actual user behavior and edge cases.",
        "critical_eye": "You are a critical-thinking QA engineer. Focus on finding gaps, ambiguities, and potential failure points in the design."
    }

    def __init__(self):
        """Initialize the test case generator with Gemini"""
        self.llm = ChatGoogleGenerativeAI(
            model=Config.LLM_MODEL,
            temperature=Config.TEMPERATURE
        )
        
    def generate_test_cases(self, requirements_text: str) -> Dict[str, Any]:
        """Generate test cases from requirements text"""
        
        prompt = ChatPromptTemplate.from_template("""
        You are an expert QA engineer. Generate comprehensive test cases from the given requirements.

        Requirements:
        {requirements}

        Generate test cases following this JSON structure:
        {{
            "summary": {{
                "total_test_cases": number,
                "functional_cases": number,
                "non_functional_cases": number,
                "edge_cases": number
            }},
            "test_cases": [
                {{
                    "id": "TC_001",
                    "title": "Test case title",
                    "type": "Functional/Performance/Security/etc",
                    "priority": "High/Medium/Low",
                    "description": "Brief description",
                    "preconditions": "Required setup",
                    "steps": ["<step 1>", "<step 2>", "<step 3>"],
                    "expected_result": "Expected outcome"
                }}
            ]
        }}

        Requirements for test case generation:
        1. Create at least 5-10 test cases
        2. Include different types: Functional, Integration, Edge cases
        3. Cover positive and negative scenarios
        4. Include appropriate priority levels
        5. Make steps clear and actionable
        6. Ensure expected results are specific
        7. IMPORTANT: Steps should be plain sentences, do NOT number them or prefix with "Step". Do not use hardcoded examples; generate steps based only on the requirements provided.

        Generate comprehensive test cases covering all aspects of the requirements.
        """)
        
        try:
            # Check API key first
            if not Config.GOOGLE_API_KEY or Config.GOOGLE_API_KEY == 'your_google_api_key_here':
                return {
                    "error": "Google API key not configured. Please set GOOGLE_API_KEY in your .env file."
                }
            
            # Generate response
            chain = prompt | self.llm
            response = chain.invoke({"requirements": requirements_text})
            
            # Parse the response
            content = response.content
            
            # Try to extract JSON from the response
            try:
                # Look for JSON in the response
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                
                if start_idx != -1 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    result = json.loads(json_str)
                    return result
                else:
                    # If no JSON found, create a basic structure
                    return {
                        "error": "Could not parse JSON response from AI",
                        "raw_response": content
                    }
                    
            except json.JSONDecodeError as e:
                return {
                    "error": f"JSON parsing error: {str(e)}",
                    "raw_response": content
                }
                
        except Exception as e:
            return {
                "error": f"Failed to generate test cases: {str(e)}"
            }
    
    def generate_test_cases_with_context(self, requirements_text: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test cases with additional context about file type and preferences"""

        # Persona prompt selection
        persona = context.get("persona")
        # Build context-aware prompt
        test_types_str = ", ".join(context.get("test_types", ["Functional"]))
        detail_level = context.get("detail_level", "Detailed")
        source_file = context.get("source_file", "unknown")
        file_type = context.get("file_type", "text")
        case_strategies = context.get("case_strategy", ["Smoke"])
        case_strategies_str = ", ".join(case_strategies)
        custom_columns = context.get("custom_columns", [])
        columns = context.get("columns", [])

        # Set detail level count based on detail level
        if detail_level == "Basic":
            num_cases = 10
        elif detail_level == "Detailed":
            num_cases = 20
        else:  # Comprehensive
            num_cases = 30

        # Build custom columns instruction
        custom_columns_instruction = ""
        if custom_columns:
            custom_columns_instruction = "\n\nCustom Columns to include:\n"
            for col in custom_columns:
                custom_columns_instruction += f"- {col.get('name', '')}: {col.get('description', '')}\n"
            custom_columns_instruction += "\nFor each custom column, generate appropriate content based on its description."

        # Use persona intro as the first line of the prompt, rest is base template
        persona_intro = self.PERSONA_INTROS.get(context.get("persona", ""), "")
        base_template = """{persona_intro}

Generate comprehensive test cases based on the requirements.

Context:
- Source: {source_file}
- File Type: {file_type}
- Test Types: {test_types}
- Case Strategies: {case_strategies}
- Detail Level: {detail_level}""" + custom_columns_instruction + """

Requirements:
{requirements}

CRITICAL: Respond ONLY with a valid JSON array. No explanations, no markdown, no extra text.

For EACH requirement or feature, create these four test case types:
1. POSITIVE: Test valid inputs and expected normal behavior
2. NEGATIVE: Test invalid inputs and proper error handling
3. BOUNDARY: Test at the limits of allowed values or conditions
4. EXPLORATORY: Test unusual or creative user actions

Format:
[
  {{
    "Test Case ID": "TC-001",
    "Type": "Functional",
    "Priority": "High",
    "Description": "Test description",
    "Test Category": "POSITIVE/NEGATIVE/BOUNDARY/EXPLORATORY",
    "Preconditions": "Setup required",
    "Steps": ["Action 1", "Action 2", "Action 3"],
    "Test Data": "Input values",
    "Expected Result": "Expected outcome",
    "Actual Result": "",
    "Status": "",
    "Comments": "",
    "Case Strategy": "{case_strategies_first}" """

        # Add custom columns to template (build as separate string to avoid template parsing issues)
        custom_columns_json = ""
        for col in custom_columns:
            col_name = col.get("name", "").replace('"', '\\"')  # Escape quotes
            col_desc = col.get("description", "").replace('"', '\\"')  # Escape quotes
            custom_columns_json += f',\n            "{col_name}": "Generated content based on: {col_desc}"'

        # Complete the template
        template_str = base_template + custom_columns_json + """
  }}
]

Generate {num_cases} test cases distributed across the four test categories and strategies: {case_strategies}
Ensure each requirement has at least one test case of each category (POSITIVE, NEGATIVE, BOUNDARY, EXPLORATORY).

IMPORTANT: DO NOT RETURN JUST A JSON ARRAY. RETURN AN OBJECT WITH THE FOLLOWING STRUCTURE (DO NOT USE MARKDOWN OR CODE BLOCKS):

EXAMPLE:
{{
  "summary": {{
    "total_test_cases": 10,
    "functional_cases": 8,
    "non_functional_cases": 2,
    "edge_cases": 3
  }},
  "test_cases": [
    {{
      "Test Case ID": "TC-001",
      "Type": "Functional",
      "Priority": "High",
      "Description": "User login with valid credentials",
      "Test Category": "POSITIVE",
      "Preconditions": "User account exists",
      "Steps": ["Navigate to login page", "Enter valid email and password", "Click login button"],
      "Test Data": {{"email": "<EMAIL>", "password": "password123"}},
      "Expected Result": "Successful login, redirection to user dashboard",
      "Actual Result": "",
      "Status": "",
      "Comments": "",
      "Case Strategy": "Smoke"
    }}
    // ...more test cases...
  ]
}}

DO NOT RETURN MARKDOWN, DO NOT RETURN A CODE BLOCK, DO NOT RETURN JUST AN ARRAY. DO NOT USE ```json OR ANY MARKDOWN FORMATTING.
ALL VALUES MUST BE VALID JSON. DO NOT USE JAVASCRIPT EXPRESSIONS LIKE .repeat(). DO NOT INCLUDE COMMENTS. DO NOT USE UNDEFINED VARIABLES. STRINGS MUST BE IN DOUBLE QUOTES. IF A VALUE IS UNKNOWN, USE AN EMPTY STRING OR NULL.
"""

        print(f"[LLM] Using persona intro: {persona_intro}")
        prompt = ChatPromptTemplate.from_template(template_str)
        
        try:
            # Check API key
            if not Config.GOOGLE_API_KEY or Config.GOOGLE_API_KEY == 'your_google_api_key_here':
                return {
                    "error": "Google API key not configured. Please set GOOGLE_API_KEY in your .env file."
                }
            
            # Generate response
            chain = prompt | self.llm
            response = chain.invoke({
                "requirements": requirements_text,
                "source_file": source_file,
                "file_type": file_type,
                "test_types": test_types_str,
                "case_strategies": case_strategies_str,
                "case_strategies_first": case_strategies[0],
                "detail_level": detail_level,
                "num_cases": num_cases,
                "persona_intro": persona_intro
            })
            
            content = response.content
            print("=== LLM RAW RESPONSE START ===")
            print(content)
            print("=== LLM RAW RESPONSE END ===")

            # First, try to extract JSON from markdown
            json_content = self._extract_json_from_markdown(content)
            
            # Then clean the response
            cleaned_content = self._clean_json_response(json_content)
            
            try:
                # Try to parse as JSON directly
                test_cases = json.loads(cleaned_content)
                if isinstance(test_cases, list):
                    return {"test_cases": test_cases}
                elif isinstance(test_cases, dict) and "test_cases" in test_cases:
                    return test_cases
                else:
                    return {"test_cases": [test_cases]}
            except json.JSONDecodeError as e:
                print(f"=== JSON DECODE ERROR: {str(e)} ===")

                # If we're still having issues, try a more direct approach
                # Look for array pattern [...]
                array_match = re.search(r'\[([\s\S]*)\]', cleaned_content, re.DOTALL)
                if array_match:
                    try:
                        array_content = "[" + array_match.group(1) + "]"
                        # Fix common JSON issues
                        array_content = self._fix_json(array_content)
                        test_cases = json.loads(array_content)
                        return {"test_cases": test_cases}
                    except json.JSONDecodeError:
                        pass
                
                # If all parsing fails, create fallback response
                return self._create_fallback_response(content, context, str(e))
                    
        except Exception as e:
            print(f"=== LLM EXCEPTION: {str(e)} ===")
            return self._create_fallback_response("", context, str(e))

    def _clean_json_response(self, content: str) -> str:
        """Clean the LLM response to make it more likely to be valid JSON"""
        # Remove markdown code block markers more thoroughly, if possible
        content = re.sub(r'^```json\s*', '', content)  # Start of JSON code block
        content = re.sub(r'\s*```$', '', content)      # End of code block
        content = re.sub(r'^```\s*', '', content)      # Start of generic code block
        
        # If the content still starts/ends with markdown markers, try a more aggressive approach
        if content.startswith('```') or content.endswith('```'):
            # Find the content between the first ``` and the last ```
            matches = re.search(r'```(?:json)?(.*?)```', content, re.DOTALL)
            if matches:
                content = matches.group(1).strip()
        
        # Remove any text before the first [ or { character
        start_idx = min(
            content.find('[') if content.find('[') != -1 else float('inf'),
            content.find('{') if content.find('{') != -1 else float('inf')
        )
        if start_idx != float('inf'):
            content = content[start_idx:]
        
        # Remove any text after the last ] or } character
        end_bracket = content.rfind(']')
        end_brace = content.rfind('}')
        end_idx = max(end_bracket, end_brace)
        if end_idx != -1:
            content = content[:end_idx + 1]
        
        return content.strip()

    def _fix_json(self, json_str: str) -> str:
        """Fix common JSON issues"""
        # Remove trailing commas
        json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
        # Fix missing commas between objects
        json_str = re.sub(r'(})\s*({)', r'\1,\2', json_str)
        # Remove comments
        json_str = re.sub(r'//.*?\n', '\n', json_str)
        # Fix invalid escape sequences (replace unescaped backslashes with double backslashes)
        # Only replace backslashes not followed by valid escape chars
        json_str = re.sub(r'\\(?!["\\/bfnrtu])', r'\\\\', json_str)
        return json_str.strip()

    def _parse_manual(self, content: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Manual parsing as fallback"""
        test_cases = []
        case_strategies = context.get("case_strategy", ["Smoke"])
        
        # Look for test case patterns
        lines = content.split('\n')
        current_case = None
        
        for line in lines:
            line = line.strip()
            if re.match(r'(TC[-_]\d+|Test Case)', line, re.IGNORECASE):
                if current_case:
                    test_cases.append(current_case)
                strat_index = len(test_cases) % len(case_strategies)
                current_case = {
                    "Test Case ID": f"TC-{len(test_cases)+1:03d}",
                    "Type": context.get("test_types", ["Functional"])[0],
                    "Priority": "Medium",
                    "Description": line,
                    "Steps": [],
                    "Expected Result": "To be verified",
                    "Case Strategy": case_strategies[strat_index],
                    "Preconditions": "",
                    "Test Data": "",
                    "Actual Result": "",
                    "Status": "",
                    "Comments": ""
                }
            elif current_case and line:
                if len(current_case["Steps"]) < 3:
                    clean_step = re.sub(r'^(Step\s*\d+\.?\s*|\d+\.\s*)', '', line, flags=re.IGNORECASE).strip()
                    if clean_step:
                        current_case["Steps"].append(clean_step)
        
        if current_case:
            test_cases.append(current_case)
            
        if not test_cases:
            raise ValueError("No test cases found")
            
        return {"test_cases": test_cases}

    def _create_fallback_response(self, content: str, context: Dict[str, Any], error_msg: str = None) -> Dict[str, Any]:
        """Create a fallback response when JSON parsing fails"""
        # Get all case frequencies for distribution
        case_strategies = context.get("case_strategy", ["Smoke"])
        
        # Create a minimal set of test cases, one for each strategy
        test_cases = []
        
        for i, strat in enumerate(case_strategies):
            test_cases.append({
                "Test Case ID": f"TC-{i+1:03d}",
                "Type": context.get("test_types", ["Functional"])[0],
                "Priority": "High",
                "Description": f"Basic {strat} test case",
                "Preconditions": "System ready",
                "Steps": ["Execute test", "Verify result", "Document outcome"],
                "Test Data": "Standard test data",
                "Expected Result": "System behaves as expected",
                "Actual Result": "",
                "Status": "",
                "Comments": "",
                "Case Strategy": strat
            })
        
        return {
            "test_cases": test_cases,
            "metadata": {
                "fallback_response": True,
                "error": error_msg or "Failed to parse LLM response",
                "generation_timestamp": datetime.now().isoformat()
            }
        }

    def _parse_simple_format(self, content: str, case_frequencies: List[str]) -> List[Dict[str, Any]]:
        """Parse the simpler format response into structured test cases"""
        import re  # Ensure re is imported here as well
        test_cases = []
        
        # Split by test case
        test_case_blocks = re.split(r'TEST CASE:', content)
        
        for i, block in enumerate(test_case_blocks):
            if not block.strip():
                continue
                
            # Create a test case dictionary
            test_case = {
                "Test Case ID": f"TC-{i+1:03d}",
                "Actual Result": "",
                "Status": "",
                "Comments": ""
            }
            
            # Extract fields
            title_match = re.search(r'(.*?)(?:TYPE:|$)', block, re.DOTALL)
            if title_match:
                test_case["title"] = title_match.group(1).strip()
                
            type_match = re.search(r'TYPE:\s*(.*?)(?:PRIORITY:|$)', block, re.DOTALL)
            if type_match:
                test_case["Type"] = type_match.group(1).strip()
                
            priority_match = re.search(r'PRIORITY:\s*(.*?)(?:DESCRIPTION:|$)', block, re.DOTALL)
            if priority_match:
                test_case["Priority"] = priority_match.group(1).strip()
                
            desc_match = re.search(r'DESCRIPTION:\s*(.*?)(?:STEPS:|$)', block, re.DOTALL)
            if desc_match:
                test_case["Description"] = desc_match.group(1).strip()
                
            steps_match = re.search(r'STEPS:(.*?)(?:EXPECTED RESULT:|$)', block, re.DOTALL)
            if steps_match:
                steps_text = steps_match.group(1).strip()
                steps = [step.strip().lstrip('-').strip() for step in steps_text.split('\n') if step.strip()]
                test_case["Steps"] = steps
            
            expected_match = re.search(r'EXPECTED RESULT:\s*(.*?)(?:CASE FREQUENCY:|$)', block, re.DOTALL)
            if expected_match:
                test_case["Expected Result"] = expected_match.group(1).strip()
            
            freq_match = re.search(r'CASE FREQUENCY:\s*(.*?)(?:$)', block, re.DOTALL)
            if freq_match:
                freq = freq_match.group(1).strip()
                # Ensure it's one of the valid frequencies
                if freq in case_frequencies:
                    test_case["Case Frequency"] = freq
                else:
                    # Assign a default frequency
                    test_case["Case Frequency"] = case_frequencies[i % len(case_frequencies)]
            else:
                # Assign a default frequency
                test_case["Case Frequency"] = case_frequencies[i % len(case_frequencies)]
            
            test_cases.append(test_case)
        
        # If no test cases were parsed, create default ones
        if not test_cases:
            for i, freq in enumerate(case_frequencies):
                test_cases.append({
                    "Test Case ID": f"TC-{i+1:03d}",
                    "title": f"Basic {freq} Test",
                    "Type": "Functional",
                    "Priority": "High",
                    "Description": f"Basic {freq} test case",
                    "Steps": ["Review requirements", "Execute test", "Verify results"],
                    "Expected Result": "System behaves as specified",
                    "Case Frequency": freq,
                    "Actual Result": "",
                    "Status": "",
                    "Comments": ""
                })
        
        return test_cases

    def _extract_json_from_markdown(self, content: str) -> str:
        """Extract JSON content from markdown code blocks"""
        # Try to find JSON code block
        json_block_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', content, re.DOTALL)
        if json_block_match:
            return json_block_match.group(1).strip()
        
        # If no code block found, return the original content
        return content

# Test the generator
if __name__ == "__main__":
    generator = TestCaseGeneratorSimple()
    
    sample_requirements = """
    User Authentication System:
    - Users must be able to register with email and password
    - Password must be at least 8 characters with special characters
    - Users can log in with valid credentials
    - Account gets locked after 3 failed login attempts
    - Users can reset password via email
    """
    
    print("Testing Gemini Test Case Generator...")
    result = generator.generate_test_cases(sample_requirements)
    
    if "error" in result:
        print(f"Error: {result['error']}")
    else:
        print(f"Generated {result.get('summary', {}).get('total_test_cases', 0)} test cases")
        
    print("✅ Test completed!")
