import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Plus, X, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface CustomColumn {
  id: string;
  name: string;
  description: string;
}

interface ColumnSelectorProps {
  defaultColumns: string[];
  selectedColumns: string[];
  customColumns: CustomColumn[];
  onColumnsChange: (columns: string[]) => void;
  onCustomColumnsChange: (customColumns: CustomColumn[]) => void;
  className?: string;
}

export const ColumnSelector: React.FC<ColumnSelectorProps> = ({
  defaultColumns,
  selectedColumns,
  customColumns,
  onColumnsChange,
  onCustomColumnsChange,
  className
}) => {
  const [showAddCustom, setShowAddCustom] = useState(false);
  const [newColumnName, setNewColumnName] = useState('');
  const [newColumnDescription, setNewColumnDescription] = useState('');

  const toggleColumn = (column: string) => {
    if (selectedColumns.includes(column)) {
      onColumnsChange(selectedColumns.filter(col => col !== column));
    } else {
      onColumnsChange([...selectedColumns, column]);
    }
  };

  const addCustomColumn = () => {
    if (newColumnName.trim() && newColumnDescription.trim()) {
      const newColumn: CustomColumn = {
        id: `custom-${Date.now()}`,
        name: newColumnName.trim(),
        description: newColumnDescription.trim()
      };
      onCustomColumnsChange([...customColumns, newColumn]);
      onColumnsChange([...selectedColumns, newColumn.name]);
      setNewColumnName('');
      setNewColumnDescription('');
      setShowAddCustom(false);
    }
  };

  const removeCustomColumn = (columnId: string) => {
    const columnToRemove = customColumns.find(col => col.id === columnId);
    if (columnToRemove) {
      onCustomColumnsChange(customColumns.filter(col => col.id !== columnId));
      onColumnsChange(selectedColumns.filter(col => col !== columnToRemove.name));
    }
  };

  return (
    <div className={cn("p-4 bg-gray-50 rounded-lg", className)}>
      <div className="flex items-center gap-2 mb-3">
        <label className="text-sm font-medium text-gray-700">
          Columns to include in the Excel:
        </label>
        <div className="group relative">
          <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
            Select default columns or add custom ones with descriptions
          </div>
        </div>
      </div>

      {/* Default Columns */}
      <div className="mb-4">
        <div className="text-xs text-gray-600 mb-2 font-medium">Default Columns:</div>
        <div className="flex flex-wrap gap-2">
          {defaultColumns.map((column) => (
            <button
              key={column}
              onClick={() => toggleColumn(column)}
              className={cn(
                "px-3 py-2 text-sm rounded-md border transition-all duration-200 hover:shadow-sm",
                selectedColumns.includes(column)
                  ? "bg-blue-100 text-blue-800 border-blue-300 shadow-sm hover:bg-blue-200"
                  : "bg-background text-foreground border-input hover:bg-accent hover:text-accent-foreground"
              )}
            >
              {column}
            </button>
          ))}
        </div>
      </div>

      {/* Custom Columns */}
      {(customColumns.length > 0 || showAddCustom) && (
        <div className="mb-4">
          <div className="text-xs text-gray-600 mb-2 font-medium">Custom Columns:</div>
          <div className="space-y-2">
            {customColumns.map((column) => (
              <div key={column.id} className="flex items-center gap-2">
                <button
                  onClick={() => toggleColumn(column.name)}
                  className={cn(
                    "flex-1 px-3 py-2 text-sm rounded-md border transition-all duration-200 hover:shadow-sm text-left",
                    selectedColumns.includes(column.name)
                      ? "bg-blue-100 text-blue-800 border-blue-300 shadow-sm hover:bg-blue-200"
                      : "bg-background text-foreground border-input hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <div className="font-medium">{column.name}</div>
                  <div className="text-xs opacity-75 mt-1">{column.description}</div>
                </button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeCustomColumn(column.id)}
                  className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add Custom Column */}
      {showAddCustom ? (
        <div className="border border-dashed border-gray-300 rounded-lg p-4 space-y-3">
          <div className="text-sm font-medium text-gray-700">Add Custom Column</div>
          <div className="space-y-2">
            <Input
              placeholder="Column name (e.g., 'Test Environment')"
              value={newColumnName}
              onChange={(e) => setNewColumnName(e.target.value)}
              className="text-sm"
            />
            <Input
              placeholder="Description (e.g., 'The specific environment where the test should be executed')"
              value={newColumnDescription}
              onChange={(e) => setNewColumnDescription(e.target.value)}
              className="text-sm"
            />
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={addCustomColumn}
              disabled={!newColumnName.trim() || !newColumnDescription.trim()}
            >
              Add Column
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setShowAddCustom(false);
                setNewColumnName('');
                setNewColumnDescription('');
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowAddCustom(true)}
          className="w-full border-dashed"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Custom Column
        </Button>
      )}

      {/* Selected columns summary */}
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="text-xs text-gray-600 mb-2">
          Selected ({selectedColumns.length} columns):
        </div>
        <div className="flex flex-wrap gap-1">
          {selectedColumns.map((column) => (
            <Badge key={column} variant="secondary" className="text-xs">
              {column}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
};
