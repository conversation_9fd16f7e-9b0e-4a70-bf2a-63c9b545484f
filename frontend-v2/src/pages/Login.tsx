import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Mail } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { GoogleLogin } from '@react-oauth/google';

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;

const Login = () => {
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const googleLoginButtonRef = useRef<HTMLDivElement>(null);

  // Google login success handler
  const handleGoogleLoginSuccess = async (credentialResponse: any) => {
    setError(null);
    setLoading(true);
    try {
      const googleToken = credentialResponse.credential;
      const res = await fetch(`${BACKEND_URL}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ google_token: googleToken }),
      });
      const data = await res.json();
      if (data.success) {
        localStorage.setItem('tudip_token', data.token);
        localStorage.setItem('tudip_user', JSON.stringify(data.user));
        navigate('/');
      } else {
        setError(data.message || 'Login failed');
      }
    } catch (err: any) {
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLoginError = () => {
    setError('Google login failed. Please try again.');
  };

  // Programmatically click the GoogleLogin button
  const handleCustomButtonClick = () => {
    if (googleLoginButtonRef.current) {
      const button = googleLoginButtonRef.current.querySelector('div[role="button"], button');
      if (button) (button as HTMLElement).click();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="flex items-center justify-center w-12 h-12 rounded-lg overflow-hidden">
              <img
                src="/favicon.ico"
                alt="Tudip Favicon"
                className="w-9 h-9 object-contain"
                style={{ background: "white", borderRadius: "6px" }}
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Testify
              </h1>
            </div>
          </div>
          <p className="text-gray-600">
            Sign in to generate AI-powered test cases
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
          <div className="text-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Welcome to Testify
            </h2>
            <p className="text-gray-600 text-sm">
              Use your Tudip mail to access the platform
            </p>
          </div>

          {/* Hidden GoogleLogin button */}
          <div ref={googleLoginButtonRef} style={{ display: 'none' }}>
            <GoogleLogin
              onSuccess={handleGoogleLoginSuccess}
              onError={handleGoogleLoginError}
              useOneTap={false}
              width="100%"
              text="signin_with"
              shape="rectangular"
              theme="filled_blue"
              logo_alignment="center"
            />
          </div>

          <Button
            onClick={handleCustomButtonClick}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 h-12"
            disabled={loading}
          >
            <Mail className="w-5 h-5 mr-3" />
            Login with Tudip Email
          </Button>
          {loading && <div className="mt-4 text-blue-600 text-center">Logging in...</div>}
          {error && <div className="mt-4 text-red-600 text-center">{error}</div>}
        </div>
      </div>
    </div>
  );
};

export default Login;
