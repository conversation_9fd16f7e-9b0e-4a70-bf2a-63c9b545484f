
import React, { useState } from 'react';
import Header from '@/components/Header';
import TabNavigation from '@/components/TabNavigation';
import UploadDocuments from '@/components/UploadDocuments';
import DirectTextInput from '@/components/DirectTextInput';

const Index = () => {
  const [activeTab, setActiveTab] = useState<'upload' | 'text'>('upload');
  const [selectedPersona, setSelectedPersona] = useState('thorough_validator');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Header />
      
      <main className="container mx-auto px-6 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Transform Your Requirements into Test Cases
            </h2>
            <p className="text-lg text-gray-600">
              Upload your PRD/BRD documents or paste content directly to generate comprehensive AI-powered test cases
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="mb-6">
              <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
            </div>

            

            <div className="min-h-[400px]">
              {activeTab === 'upload' ? <UploadDocuments /> : <DirectTextInput />}
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              • Secure document processing • Generate comprehensive test scenarios
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;
